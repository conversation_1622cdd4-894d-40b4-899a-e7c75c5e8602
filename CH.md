# **модернизация ChartGenius · Оркестрация через n8n**

**Техническое задание для разработчика **

## **0\. Первоочередной бриф для разработчика**

**Цель:** Сформировать чёткую картину текущего фронтенда и бэкенда, **декомпозировать** бэкенд на атомарные сервисы, определить, что идёт в n8n, а что — в Python-сервис(ы), и подготовить инфраструктурный скелет (Docker/Compose) для всей системы.

### **Шаг 1: Инвентаризация интеграций фронтенда**

* Выявить все точки взаимодействия фронтенда с бэкендом и LLM (REST API, Webhooks).
* определить какой ответ ожидает фронтенд для корректной отрисовки.  
* Детально описать контракты (JSON-схемы) для каждого эндпоинта.  
* Зафиксировать все передаваемые параметры.
* Подтвердить требования фронтенда к формату ответа, чтобы обеспечить обратную совместимость. 

### **Шаг 2: Аудит и декомпозиция бэкенда**

* Проанализировать существующий код бэкенда и выделить ключевые модули: "сбор данных с CryptoCompare", "расчёт индикаторов", "формирование промпта", "вызов LLM".  
* Разделить эту логику на атомарные, независимые блоки: Data Fetcher, Indicators Calculator, Prompt Builder, LLM Caller (OpenAI), иные выявленые. (выбор неймига на усмотрению разработчика) 
* Для каждого блока определить зависимости: библиотеки, переменные окружения (.env), внешние API, необходимые ключи, переменные, прочее.

### **Шаг 3: Проектирование сервисной архитектуры**

* **Принять ключевое решение (см. раздел 1):** выбрать между рефакторингом существующего кода ("Расщепление") и написанием нового с нуля ("Новый сервис").  
* Для выбранного подхода спроектировать REST API для каждого микросервиса (эндпоинты, методы, контракты и прочее).  
* использовать инструменты и справочую информацию в интернете.  
* Определить, какие эндпоинты будут вызываться из n8n, а какие — напрямую из фронтенда или иных источников.  
* учитывать ограничения oracle free tier.

### **Шаг 4: Проектирование оркестрации в n8n**

* сверяться с https://docs.n8n.io/ при проектировании оркестрации.
* использовать актуальную информацию о компонентах https://docs.n8n.io/workflows/components/ в том числе специализированные ноды в соответствии с используемыми технологиями.
* Спроектировать master workflow и все необходимые под-воркфлоу (auth, fetch+calc, call\_llm, merge+save, status, errors, cleanup и прочие необходимые).  
* Определить, как атомарные Python-сервисы будут вызываться из n8n (через ноду HTTP Request, Execute Workflow, иное).  
* Заложить в архитектуру воркфлоу политики повторных попыток (retry), таймауты и ограничения на параллельное выполнение (Concurrency).

### **Шаг 5: Подготовка инфраструктуры**

* Сформировать docker-compose.yml, включающий все необходимые сервисы: Postgres, Python-сервис(ы), API-Gateway, Nginx, Redis.
* учитывать, что n8n уже запущен на отдельной VM и доступен по адресу https://chartgenius.ru.
* Написать Dockerfile для каждого кастомного сервиса.  
* Подготовить переменные окружения (.env) и описать процесс управления секретами (через n8n Credentials и переменные окружения Docker).
* для настройки и подготовки инфраструктуры использовать инструменты, такие как playwright и n8n cli и справочую информацию в интернете.

### **Шаг 6: Итоговые артефакты по этапу**

По завершении этого этапа разработчик должен предоставить:

* **Карту интеграций:** Диаграмма или таблица, описывающая все взаимодействия.  
* **Схему сервисов:** Таблица с описанием микросервисов, их эндпоинтов и Docker-образов. Описание всех эндпоинтов, их контрактов и ожидаемых ответов. 
* **Проект воркфлоу n8n:** Экспорт JSON-файлов для импорта в n8n. Описание всех воркфлоу, их структуры и взаимодействия. JSON-схемы для всех эндпоинтов и структур данных.
* **OpenAPI/Swagger спецификацию** для разработанных микросервисов.  
* **ADR-документ** с решением по стратегии развития бэкенда.

## **1\. Стратегия развития бэкенда: Исследование и выбор**

Прежде чем приступить к реализации, необходимо выбрать один из двух подходов к развитию бэкенда.

* **Вариант А: "Расщепление" (Refactor & Split).**  
  * **Суть:** Постепенный рефакторинг существующего монолита. Выделение логических модулей и их "обертывание" в REST API эндпоинты.  
  * **Плюсы:** Быстрое получение результата, переиспользование отлаженного кода.  
  * **Минусы:** Риск унаследовать технический долг, скрытые зависимости, сложность в дальнейшей поддержке.  
* **Вариант Б: "Новый сервис" (Greenfield Backend).**  
  * **Суть:** Написание нового бэкенда с нуля по принципам микросервисной архитектуры. Из старого кода заимствуются только проверенные алгоритмы в виде библиотек.  
  * **Плюсы:** Чистая, тестируемая и хорошо документированная архитектура.  
  * **Минусы:** Требует больше времени на первоначальную разработку.

**Итоговый артефакт:** Документ ADR (Architecture Decision Record) с таблицей сравнения, оценкой рисков и финальной, аргументированной рекомендацией в пользу одного из подходов.

## **2\. Краткий обзор (TL;DR)**

* **Задача:** Создать управляемый pipeline для анализа крипторынка, который по запросу пользователя отдает график с индикаторами и текстовый анализ от LLM.  
* **n8n — Оркестратор:** Ядро системы, управляющее задачами, аутентификацией и лимитами.  
* **Python-микросервис (FastAPI):** Выполняет ресурсоемкие задачи: загрузку данных, расчет индикаторов и обращение к LLM.  
* **PostgreSQL — Хранилище:** Содержит все данные: пользователей, задачи, подписки, логи, метрики и шаблоны промптов.  
* **Асинхронность:** Система мгновенно возвращает task\_id, позволяя пользователю получить результат позже.
* **Мониторинг и логирование:** без раздувания существующего технологического стека.

## **3\. Scope, Out of Scope и Backlog**

### **3.1 В Scope**

* Оркестрация всего процесса через n8n (workflows, credentials, variables, etc.).  
* Интеграция с Telegram (WebApp \+ Login Widget для браузера \+ Bot).  
* Python-микросервис на FastAPI.  
* Хранение всех данных в Postgres.  
* Реализация очередей и лимитов для пользователей.  
* Интеграция с платежной системой Tribute для управления подписками.  
* Система уведомлений через Telegram-бота.
* логирование для дебага и мониторинга.
* **Личный кабинет (Web UI):** История запросов, управление тарифами, профиль. 
* **Админ-панель:** CRUD пользователей, управление лимитами, просмотр задач и ошибок, дашборды с метриками. 

### **3.2 Вне Scope (на данном этапе)**

* WebSocket и push-уведомления во фронтенд.  
* Мультиязычность.  
* Расширенные функции шаринга результатов (публичные ссылки, экспорт в PDF/PNG).

### **3.3 Backlog (задачи на будущее)**
 
* **Расширенная система ролей:** Модераторы, поддержка.  
* **IaC (Infrastructure as Code):** Управление инфраструктурой через Terraform/Ansible.  
* **Расширенный мониторинг:** OpenTelemetry, Sentry, Grafana.

## **4\. Требования**

### **4.1 Бизнес-требования**

1. Пользователь запускает анализ через Telegram WebApp или браузер.  
2. Сервис мгновенно (\< 2 сек) возвращает task\_id.  
3. Полный результат анализа готов в течение 5 минут.  
4. Система выдерживает десятки одновременных задач.  
5. Ведется учет подписок и лимитов запросов.
6. используется текущий дизайн и UI/UX фронтенда.

### **4.2 Нефункциональные требования (NFR)**

| Категория | Требование | Метрика/Ограничение | Механизм выполнения |
| :---- | :---- | :---- | :---- |
| **Производительность** | Время ответа на запрос (ACK) | \< 2 сек | n8n Webhook мгновенно отвечает и запускает асинхронный воркфлоу. |
|  | Время полной обработки | P95 ≤ 5 минут | Асинхронная архитектура, таймауты на узлах, политика ретраев. |
| **Масштабируемость** | Одновременные задачи | 20–30 на VM | n8n Concurrency Control. При росте — переход в Queue Mode с Redis. |
| **Надёжность** | Потеря задач при сбоях | 0% | Все статусы фиксируются в Postgres. Настроены ретраи и Error Handler. |
| **Безопасность** | Валидация входов | 100% корректность | Проверка HMAC-подписи от Telegram и подписи от Tribute. |
|  | Хранение секретов | Не хранить в коде/JSON | Использование .env, n8n Credentials и RBAC. |
| **Наблюдаемость** | Логирование | 100% ключевых событий | Таблицы tasks, errors, metrics в Postgres \+ логи n8n. |

## **5\. Архитектура и Аутентификация**

### **5.1 Архитектурная схема (пример, итоговая реализация по итогам исследования)**

flowchart LR  
  subgraph User Interface  
    TG\[Telegram Bot/WebApp\]  
    Browser\[Браузер\]  
  end

  subgraph Backend  
    API\[API Gateway (FastAPI)\]  
    N8N\[n8n Оркестратор\]  
    PY\[Python Microservice\]  
    DB\[(PostgreSQL)\]  
  end  
    
  subgraph External Services  
    CryptoCompare\[CryptoCompare API\]  
    OpenAI\[OpenAI API\]  
    Tribute\[Tribute Payments\]  
  end

  User\_Interface \--\>|1. Запрос на анализ| API  
  API \--\>|2. Валидация, JWT, вызов Webhook| N8N  
  N8N \--\>|3. Запуск под-воркфлоу| PY  
  PY \--\>|4. Запрос данных| CryptoCompare  
  PY \--\>|5. Запрос к LLM| OpenAI  
  PY \--\>|6. Возврат результата| N8N  
  N8N \--\>|7. Сохранение статуса и результата| DB  
  API \--\>|8. Проверка статуса по task\_id| DB  
  Tribute \--\>|Webhook об оплате| API

### **5.2 Аутентификация**

* **Telegram WebApp:** Фронтенд отправляет initData на бэкенд (API Gateway). Бэкенд валидирует HMAC-SHA256 подпись с помощью bot\_token и в случае успеха выдает JWT.  
* **Браузер:** Используется Telegram Login Widget. Бэкенд получает данные пользователя, валидирует их и также выдает JWT.  
* **n8n:** Все запросы к n8n должны содержать валидный JWT. Первый шаг в любом воркфлоу — проверка токена и прав доступа пользователя (лимиты, подписка). В n8n необходимо включить **RBAC (Role-based access control)** для разграничения доступа к воркфлоу и credentials.

## **6\. База данных (PostgreSQL) (пример, итоговая реализация по итогам исследования)**

Схема таблиц включает users, tasks, temp\_chart\_data, errors, metrics, subscriptions, prompts.

\-- Пользователи системы  
CREATE TABLE users (  
    user\_id        TEXT PRIMARY KEY,     \-- Telegram User ID  
    username       TEXT,  
    is\_premium     BOOLEAN NOT NULL DEFAULT FALSE,  
    subscription\_tier TEXT NOT NULL DEFAULT 'free', \-- free, premium, vip  
    requests\_limit INT NOT NULL DEFAULT 50,  
    created\_at     TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),  
    updated\_at     TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW()  
);

\-- Задачи на анализ  
CREATE TABLE tasks (  
    task\_id        UUID PRIMARY KEY,  
    user\_id        TEXT NOT NULL REFERENCES users(user\_id),  
    status         TEXT NOT NULL CHECK (status IN ('queued','running','done','failed','timeout')),  
    symbol         TEXT NOT NULL,  
    timeframe      TEXT NOT NULL,  
    candles\_count  INT  NOT NULL,  
    indicators     TEXT\[\] NOT NULL,  
    prompt\_template\_version TEXT,  
    created\_at     TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),  
    started\_at     TIMESTAMP WITH TIME ZONE,  
    finished\_at    TIMESTAMP WITH TIME ZONE,  
    error          TEXT,  
    result\_json    JSONB  
);

\-- Временные данные для сборки ответа  
CREATE TABLE temp\_chart\_data (  
    task\_id     UUID PRIMARY KEY REFERENCES tasks(task\_id) ON DELETE CASCADE,  
    data\_json   JSONB NOT NULL,  
    expires\_at  TIMESTAMP WITH TIME ZONE NOT NULL  
);

\-- Логирование ошибок  
CREATE TABLE errors (  
  id SERIAL PRIMARY KEY,  
  task\_id UUID,  
  workflow TEXT,  
  node TEXT,  
  error TEXT,  
  created\_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW()  
);

\-- Сбор метрик производительности  
CREATE TABLE metrics (  
  id SERIAL PRIMARY KEY,  
  task\_id UUID,  
  duration\_ms INT,  
  tokens\_in INT,  
  tokens\_out INT,  
  created\_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW()  
);

\-- Подписки пользователей  
CREATE TABLE subscriptions (  
  id SERIAL PRIMARY KEY,  
  user\_id TEXT NOT NULL REFERENCES users(user\_id),  
  plan TEXT NOT NULL,  
  status TEXT NOT NULL, \-- active, expired, canceled  
  started\_at TIMESTAMP WITH TIME ZONE NOT NULL,  
  expires\_at TIMESTAMP WITH TIME ZONE NOT NULL,  
  source TEXT NOT NULL DEFAULT 'tribute',  
  tx\_id TEXT,  
  raw\_event JSONB  
);

\-- Шаблоны промптов  
CREATE TABLE prompts (  
    id SERIAL PRIMARY KEY,  
    name TEXT NOT NULL UNIQUE,  
    version INT NOT NULL,  
    body TEXT NOT NULL,  
    is\_active BOOLEAN NOT NULL DEFAULT FALSE,  
    created\_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),  
    UNIQUE(name, version)  
);

## **7\. Потоки (Workflows) в n8n**

### **7.1 Каталог воркфлоу (пример, итоговая реализация по итогам исследования)**

| Код | Название | Тип | Назначение |
| :---- | :---- | :---- | :---- |
| **WF\_MASTER** | wf\_user\_request\_analysis | Webhook → Async | Приём запроса, создание task, запуск основной обработки. |
| **WF\_AUTH\_CHECK** | wf\_auth\_check | Subworkflow | Проверка JWT, лимитов, подписки пользователя. |
| **WF\_FETCH\_AND\_CALC** | wf\_fetch\_and\_calc | Subworkflow | Получение свечей, расчет индикаторов. |
| **WF\_CALL\_LLM** | wf\_call\_llm | Subworkflow | Построение промпта и вызов LLM. |
| **WF\_MERGE\_AND\_SAVE** | wf\_merge\_and\_save | Subworkflow | Сборка финального result\_json и сохранение в БД. |
| **WF\_STATUS** | wf\_status | Webhook | Отдача статуса/результата по task\_id. |
| **WF\_ERROR\_HANDLER** | wf\_error\_handler | Error Trigger | Централизованный логгер ошибок. |
| **WF\_CLEANUP** | wf\_cleanup\_temporary | Cron | Периодическая очистка temp\_chart\_data. |

### **7.2 Детализация WF\_MASTER: wf\_user\_request\_analysis (пример, итоговая реализация по итогам исследования)**

1. **Webhook (POST /analysis/request)**: Принимает запрос.  
2. **Function "Validate & Gen ID"**:  
   const { symbol, timeframe, candles\_count } \= $json;  
   if (\!symbol || \!timeframe || \!candles\_count) {  
     throw new Error('symbol, timeframe, candles\_count are required');  
   }  
   const { v4: uuidv4 } \= require('uuid');  
   $json.task\_id \= uuidv4();  
   return $json;

3. **Execute Workflow "WF\_AUTH\_CHECK"**: Проверяет права.  
4. **Postgres "Insert Task"**: Создает запись в tasks со статусом queued.  
5. **Execute Workflow "WF\_FETCH\_AND\_CALC"**: Асинхронно запускает основную цепочку.  
6. **Respond to Webhook**: Мгновенно возвращает 202 Accepted с task\_id.

## **8\. Python Микросервис (FastAPI)**

### **8.1 Требования**

* Реализовать **rate limiting** на эндпоинтах.  
* Логировать количество токенов (in/out) для каждого вызова LLM в таблицу metrics.  
* Обеспечить строгую валидацию ответа от LLM на соответствие JSON-схеме.  
* Включить сквозную трассировку по task\_id во всех логах сервиса.

### **8.2 Логика LLM (llm.py) (пример, итоговая реализация по итогам исследования)**

import os  
import json  
from openai import OpenAI

client \= OpenAI(api\_key=os.getenv("OPENAI\_API\_KEY"))

def call\_llm(model: str, system\_prompt: str, user\_prompt: str) \-\> str:  
    """  
    Вызывает модель OpenAI с системным и пользовательским промптом в JSON mode.  
    """  
    completion \= client.chat.completions.create(  
        model=model,  
        messages=\[  
            {"role": "system", "content": system\_prompt},  
            {"role": "user", "content": user\_prompt}  
        \],  
        temperature=0.2,  
        max\_tokens=2048,  
        response\_format={"type": "json\_object"}  
    )  
    \# Здесь же можно логировать usage.total\_tokens в метрики  
    return completion.choices\[0\].message.content

## **9\. Интеграция с платежной системой Tribute**

* **Задача:** Принимать оплату за подписки и пополнение лимитов через @Tribute (Telegram Stars).  
* **Webhook:** Настроить в FastAPI эндпоинт /payments/tribute/webhook для приема событий от Tribute.  
* **Валидация подписи:** Каждый запрос от Tribute содержит заголовок trbt-signature. Необходимо проверять его подлинность по формуле HMAC-SHA256(raw\_body, API\_KEY).  
* **Обновление данных:** При успешной оплате обновлять данные в таблицах subscriptions и users.

## **10\. Инфраструктура, CI/CD и Тестирование**

### **10.1 docker-compose.yml (пример, итоговая реализация по итогам исследования)**

version: "3.9"  
services:  
  postgres:  
    image: postgres:16  
    environment:  
      POSTGRES\_DB: chartgenius  
      POSTGRES\_USER: chartgenius  
      POSTGRES\_PASSWORD: ${POSTGRES\_PASSWORD}  
    volumes:  
      \- pgdata:/var/lib/postgresql/data  
    restart: unless-stopped

  pyservice:  
    build: ./pyservice  
    environment:  
      OPENAI\_API\_KEY: ${OPENAI\_API\_KEY}  
    depends\_on: \[postgres\]  
    restart: unless-stopped

  n8n:  
    image: n8nio/n8n:latest  
    environment:  
      N8N\_BASIC\_AUTH\_USER: ${N8N\_USER}  
      N8N\_BASIC\_AUTH\_PASSWORD: ${N8N\_PASSWORD}  
      DB\_TYPE: postgresdb  
      DB\_POSTGRESDB\_HOST: postgres  
      DB\_POSTGRESDB\_DATABASE: chartgenius  
      DB\_POSTGRESDB\_USER: chartgenius  
      DB\_POSTGRESDB\_PASSWORD: ${POSTGRES\_PASSWORD}  
      N8N\_HOST: ${N8N\_HOST}  
      WEBHOOK\_URL: https://${N8N\_HOST}/  
      N8N\_GIT\_ENABLED: "true"  
    ports: \["5678:5678"\]  
    depends\_on: \[postgres\]  
    volumes: \[n8n\_data:/home/<USER>/.n8n\]  
    restart: unless-stopped  
\# ... api\_gateway, nginx  
volumes:  
  pgdata:  
  n8n\_data:

### **10.2 CI/CD (откладывается до восстановления доступа к GitHub)**

* **Репозиторий:** GitHub.  
* **Процесс:** На push в main запускается GitHub Actions для pyservice: линтинг, тесты, сборка и push Docker-образа. Изменения в n8n-воркфлоу синхронизируются через Git Sync.

### **10.3 Тестирование**

* **Unit-тесты (pytest):** Для всей бизнес-логики в Python-сервисе.  
* **Интеграционные тесты (Postman/Newman):** Проверка всей цепочки от /analysis/request до получения результата. Тестирование негативных сценариев (неверный JWT, превышение лимита).  
* **Нагрузочные тесты (k6/locust):** Эмуляция 30 параллельных запросов для проверки стабильности и работы очередей.

## **11\. План внедрения**

### **11.2 План внедрения (Roadmap)**

1. **F1. Подготовка:** Настройка локального окружения (Docker, n8n, Postgres).  
2. **F2. Базовые WF:** Реализация MASTER, AUTH, STATUS, ERROR.  
3. **F3. Основная логика:** Реализация FETCH\_AND\_CALC, CALL\_LLM, MERGE\_AND\_SAVE.  
4. **F4. Интеграция:** Полный e2e тест с фронтендом.  
5. **F5. Эксплуатация:** Настройка очистки, метрик, уведомлений.  
6. **F6. Прод:** Деплой на сервер, настройка доменов/HTTPS, мониторинг.

## **12\. Приложения**

### **12.1 Пример экспорта Workflow (JSON) (пример, итоговая реализация по итогам исследования)**

Это пример для WF\_MASTER. Реальные ID нод будут сгенерированы n8n при импорте.

{  
  "name": "wf\_user\_request\_analysis",  
  "nodes": \[  
    {  
      "parameters": {  
        "path": "analysis/request",  
        "methods": \["POST"\],  
        "responseMode": "lastNode"  
      },  
      "id": "Webhook\_Entry",  
      "name": "Webhook Entry",  
      "type": "n8n-nodes-base.webhook",  
      "typeVersion": 1,  
      "position": \[200, 300\]  
    },  
    {  
      "parameters": {  
        "functionCode": "const { v4: uuidv4 } \= require('uuid');\\nconst { symbol, timeframe, candles\_count, user\_id } \= $json;\\nif(\!symbol||\!timeframe||\!candles\_count) {\\n  throw new Error('symbol,timeframe,candles\_count обязательны');\\n}\\nconst task\_id \= uuidv4();\\nreturn \[{ json: { ...$json, task\_id } }\];"  
      },  
      "id": "Function\_Validate",  
      "name": "Validate & Gen ID",  
      "type": "n8n-nodes-base.function",  
      "typeVersion": 2,  
      "position": \[450, 300\]  
    },  
    {  
      "parameters": {  
        "operation": "insert",  
        "table": "tasks",  
        "columns": "task\_id,user\_id,status,symbol,timeframe,candles\_count",  
        "values": "={{$json\[\\"task\_id\\"\]}},{{$json\[\\"user\_id\\"\]}},'queued',{{$json\[\\"symbol\\"\]}},{{$json\[\\"timeframe\\"\]}},{{$json\[\\"candles\_count\\"\]}}"  
      },  
      "id": "DB\_InsertTask",  
      "name": "DB Insert Task",  
      "type": "n8n-nodes-base.postgres",  
      "typeVersion": 1,  
      "position": \[700, 300\],  
      "credentials": {  
        "postgres": "chartgenius-db"  
      }  
    },  
    {  
      "parameters": {  
        "workflowId": "ID\_ВОРКФЛОУ\_FETCH\_AND\_CALC",  
        "options": {  
          "waitForReturnData": false  
        }  
      },  
      "id": "Exec\_FetchCalc",  
      "name": "Fetch+Calc (async)",  
      "type": "n8n-nodes-base.executeWorkflow",  
      "typeVersion": 1,  
      "position": \[950, 300\]  
    },  
    {  
      "parameters": {  
        "responseBody": "={ \\"status\\":\\"accepted\\", \\"task\_id\\": $json.task\_id }",  
        "responseCode": 202  
      },  
      "id": "Respond\_ACK",  
      "name": "Respond ACK",  
      "type": "n8n-nodes-base.respondToWebhook",  
      "typeVersion": 1,  
      "position": \[1200, 300\]  
    }  
  \],  
  "connections": {  
    "Webhook\_Entry": {"main": \[\[{"node": "Function\_Validate","type": "main","index": 0}\]\]},  
    "Function\_Validate": {"main": \[\[{"node": "DB\_InsertTask","type": "main","index": 0}\]\]},  
    "DB\_InsertTask": {"main": \[\[{"node": "Exec\_FetchCalc","type": "main","index": 0}\]\]},  
    "Exec\_FetchCalc": {"main": \[\[{"node": "Respond\_ACK","type": "main","index": 0}\]\]}  
  }  
}

### **12.2 Зафиксированные архитектурные решения**

* **Шаблон промпта:** Хранится в таблице prompts в Postgres.  
* **Валидация подписи Tribute:** Реализуется на стороне API Gateway (FastAPI).